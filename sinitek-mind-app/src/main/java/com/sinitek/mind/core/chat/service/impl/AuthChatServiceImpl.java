package com.sinitek.mind.core.chat.service.impl;

import com.sinitek.mind.core.chat.service.IAuthChatService;
import com.sinitek.sirm.framework.exception.BussinessException;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

@Service
public class AuthChatServiceImpl implements IAuthChatService {
    @Override
    public void authChatCrud(String appId, String chatId, long per) {
        if (StringUtils.isBlank(appId)) {
            throw new BussinessException("appId is empty， valid chat is faild");
        }
        // TODO 验证chatCrud

    }
}
