package com.sinitek.mind.dataset.controller;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.sinitek.mind.common.support.ApiResponse;
import com.sinitek.mind.dataset.dto.DatasetDTO;
import com.sinitek.mind.dataset.dto.DatasetListRequest;
import com.sinitek.mind.dataset.dto.DatasetUpdateRequest;
import com.sinitek.mind.dataset.service.IDatasetService;
import com.sinitek.mind.model.service.ISystemModelService;
import com.sinitek.mind.model.utils.ModelTestDataUtil;
import com.sinitek.mind.system.service.AuthService;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.request.MockHttpServletRequestBuilder;

import static org.junit.jupiter.api.Assertions.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

/**
 * DatasetController-单元测试
 *
 * <AUTHOR>
 * date 2025-07-30
 */
public class DatasetControllerTest extends CommonDatasetTest {

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private AuthService authService;

    @Autowired
    private ISystemModelService systemModelService;

    @Autowired
    private IDatasetService datasetService;

    private final String TEST_CRUD_DATASET_NAME = "立马删除的知识库";

    @Test
    @DisplayName("测试知识库列表")
    public void getDatasetList() throws Exception {
        DatasetListRequest listRequest = new DatasetListRequest();
        // 设置列表查询参数

        MockHttpServletRequestBuilder request = postMockRequestForAdmin("/mind/api/core/dataset/list")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(listRequest));

        MvcResult result = mockMvc.perform(request)
                .andExpect(status().isOk())
                .andReturn();

        String responseContent = result.getResponse().getContentAsString();
        ApiResponse<?> apiResponse = objectMapper.readValue(responseContent, new TypeReference<ApiResponse<?>>() {});
        assertTrue(apiResponse.isSuccess(), "获取知识库列表失败");
        assertNotNull(apiResponse.getData(), "知识库列表数据为空");
    }

    @Test
    @DisplayName("测试知识库CRUD流程")
    public void testDatasetCrud() throws Exception {
        String datasetId = createDataset(TEST_CRUD_DATASET_NAME, ModelTestDataUtil.LLM_TEST_MODEL_ID, ModelTestDataUtil.EMBEDDING_TEST_MODEL_ID);

        // 测试获取详情
        getDatasetDetail(datasetId);

        // 测试更新知识库
        updateDataset(datasetId);

        // 测试删除知识库
        deleteDataset(datasetId);
    }

    /**
     * 获取知识库详情
     */
    private void getDatasetDetail(String id) throws Exception {
        MockHttpServletRequestBuilder request = getMockRequestForAdmin("/mind/api/core/dataset/detail")
                .param("id", id);

        MvcResult result = mockMvc.perform(request)
                .andExpect(status().isOk())
                .andReturn();

        String responseContent = result.getResponse().getContentAsString();
        ApiResponse<DatasetDTO> apiResponse = objectMapper.readValue(responseContent, new TypeReference<ApiResponse<DatasetDTO>>() {});
        assertTrue(apiResponse.isSuccess(), "获取知识库详情失败");
        assertNotNull(apiResponse.getData(), "知识库详情数据为空");
        assertEquals(TEST_CRUD_DATASET_NAME, apiResponse.getData().getName(), "知识库名称不匹配");
    }

    /**
     * 更新知识库
     */
    private void updateDataset(String id) throws Exception {
        DatasetUpdateRequest updateRequest = new DatasetUpdateRequest();
        updateRequest.setId(id);
        updateRequest.setIntro(TEST_CRUD_DATASET_NAME);
        

        MockHttpServletRequestBuilder request = postMockRequestForAdmin("/mind/api/core/dataset/update")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(updateRequest));

        MvcResult result = mockMvc.perform(request)
                .andExpect(status().isOk())
                .andReturn();

        String responseContent = result.getResponse().getContentAsString();
        ApiResponse<?> apiResponse = objectMapper.readValue(responseContent, new TypeReference<ApiResponse<?>>() {});
        assertTrue(apiResponse.isSuccess(), "更新知识库失败");

        // 验证更新结果
        getDatasetDetailAfterUpdate(id);
    }

    /**
     * 验证更新后的知识库详情
     */
    private void getDatasetDetailAfterUpdate(String id) throws Exception {
        MockHttpServletRequestBuilder request = getMockRequestForAdmin("/mind/api/core/dataset/detail")
                .param("id", id);

        MvcResult result = mockMvc.perform(request)
                .andExpect(status().isOk())
                .andReturn();

        String responseContent = result.getResponse().getContentAsString();
        ApiResponse<DatasetDTO> apiResponse = objectMapper.readValue(responseContent, new TypeReference<ApiResponse<DatasetDTO>>() {});
        assertEquals(TEST_CRUD_DATASET_NAME, apiResponse.getData().getIntro(), "知识库描述更新失败");
    }

    /**
     * 删除知识库
     */
    private void deleteDataset(String id) throws Exception {
        MockHttpServletRequestBuilder request = postMockRequestForAdmin("/mind/api/core/dataset/delete")
                .param("id", id);

        MvcResult result = mockMvc.perform(request)
                .andExpect(status().isOk())
                .andReturn();

        String responseContent = result.getResponse().getContentAsString();
        ApiResponse<?> apiResponse = objectMapper.readValue(responseContent, new TypeReference<ApiResponse<?>>() {});
        assertTrue(apiResponse.isSuccess(), "删除知识库失败");

        // 验证删除结果
        verifyDatasetDeleted(id);
    }

    /**
     * 验证知识库已删除
     */
    private void verifyDatasetDeleted(String id) throws Exception {
        MockHttpServletRequestBuilder request = getMockRequestForAdmin("/mind/api/core/dataset/detail")
                .param("id", id);

        MvcResult result = mockMvc.perform(request)
                .andExpect(status().isOk())
                .andReturn();

        String responseContent = result.getResponse().getContentAsString();
        ApiResponse<?> apiResponse = objectMapper.readValue(responseContent, ApiResponse.class);
        assertFalse(apiResponse.isSuccess(), "删除后仍能查询到知识库详情");
    }
}