package com.sinitek.mind.support.common.controller;

import com.sinitek.mind.common.dto.FileTokenQuery;
import com.sinitek.mind.common.support.ApiResponse;
import com.sinitek.mind.common.util.MindFileTokenUtil;
import com.sinitek.mind.support.common.constant.FileConstant;
import com.sinitek.mind.support.common.dto.FileInfo;
import com.sinitek.mind.support.common.dto.UploadFileResponse;
import com.sinitek.mind.support.common.dto.UploadImageRequest;
import com.sinitek.mind.support.common.service.IFileService;
import com.sinitek.sirm.common.utils.HttpUtils;
import com.sinitek.sirm.common.utils.JsonUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.io.FilenameUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.HashMap;
import java.util.Map;

/**
 * 文件控制器
 *
 * <AUTHOR>
 * @date 2025/1/27
 */
@Slf4j
@RestController
@RequestMapping("/mind/api/common/file")
@Tag(name = "文件管理", description = "文件上传下载相关接口")
@Validated
public class FileController {

    @Autowired
    private IFileService fileService;

    @Autowired
    private MindFileTokenUtil mindFileTokenUtil;

    /**
     * 上传图片
     *
     * @param request 上传请求
     * @return 上传结果
     */
    @PostMapping("/uploadImage")
    @Operation(summary = "上传图片", description = "通过base64编码上传图片文件")
    public ApiResponse<String> uploadImage(@Valid @RequestBody UploadImageRequest request) {
        log.info("接收到图片上传请求");
        
        try {
            String response = fileService.uploadImage(request);
            return ApiResponse.success(response);
        } catch (Exception e) {
            log.error("图片上传失败", e);
            return ApiResponse.error("图片上传失败: " + e.getMessage());
        }
    }

    @GetMapping("/downloadImage/{sourceId}")
    @Operation(summary = "获取图片", description = "获取指定sourceId的图片")
    public ResponseEntity<Resource> downloadImage(@PathVariable Long sourceId) {
        log.info("接收到文件下载请求，sourceId: {}", sourceId);
        
        try {
            Resource fileResource = fileService.downloadImage(sourceId);
            
            if (fileResource == null || !fileResource.exists()) {
                return ResponseEntity.notFound().build();
            }
            
            // 设置响应头
            String filename = fileResource.getFilename();
            HttpHeaders headers = new HttpHeaders();
            headers.add(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + filename + "\"");
            
            // 根据文件扩展名设置Content-Type
            MediaType mediaType = getMediaTypeByFilename(filename);
            
            return ResponseEntity.ok()
                    .headers(headers)
                    .contentType(mediaType)
                    .body(fileResource);
                    
        } catch (Exception e) {
            log.error("文件下载失败，sourceId: {}", sourceId, e);
            return ResponseEntity.internalServerError().build();
        }
    }

    @PostMapping("/upload")
    @Operation(summary = "通用文件上传", description = "上传文件到指定桶，支持chat和dataset")
    public ApiResponse<UploadFileResponse> uploadFile(
            @RequestParam("file") MultipartFile file,
            @RequestParam("bucketName") String bucketName,
            @RequestParam(name = "data", required = false) String data) {

        Map<String, Object> jsonMap = JsonUtil.toMap(data);
        Map<String, String> metadata = new HashMap<>();
        if (MapUtils.isNotEmpty(jsonMap)) {
            for (String key : jsonMap.keySet()) {
                metadata.put(key, MapUtils.getString(jsonMap, key));
            }
        }
        return ApiResponse.success(fileService.uploadFile(file, bucketName, metadata));
    }

    /**
     * 文件读取接口
     *
     * <AUTHOR>
     * date 2025-07-17
     */
    @GetMapping("/read")
    @Operation(summary = "读取文件", description = "根据token读取文件，支持某些扩展名的预览")
    public void readFile(@RequestParam("token") String token, HttpServletResponse response)  {
        log.info("接收到文件读取请求，token: {}", token);
        // 1. 认证token，获取bucketName、fileId等
        FileTokenQuery fileTokenQuery = mindFileTokenUtil.authFileToken(token);
        String bucketName = fileTokenQuery.getBucketName();
        String fileId = fileTokenQuery.getFileId();

        // 2. 获取文件信息
        FileInfo fileInfo = fileService.getFileById(bucketName, fileId);
        if (fileInfo == null) {
            log.error("文件未找到: {}", fileId);
        }

        String filename = fileInfo.getFilename();
        String extension = FilenameUtils.getExtension(filename);
        File sourceFile = fileInfo.getSourceFile();
        try (InputStream inputStream = new FileInputStream(sourceFile)) {
            // 支持预览的文件类型，则预览方式
            if (FileConstant.PRE_VIEWABLE_EXTENSIONS.contains(extension.toLowerCase())) {
                HttpUtils.open(response, filename, inputStream);
            } else {
                HttpUtils.download(response, filename, inputStream);
            }
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }
    
    /**
     * 根据文件名获取MediaType
     */
    private MediaType getMediaTypeByFilename(String filename) {
        if (filename == null) {
            return MediaType.APPLICATION_OCTET_STREAM;
        }
        
        String extension = filename.substring(filename.lastIndexOf(".") + 1).toLowerCase();
        switch (extension) {
            case "jpg":
            case "jpeg":
                return MediaType.IMAGE_JPEG;
            case "png":
                return MediaType.IMAGE_PNG;
            case "gif":
                return MediaType.IMAGE_GIF;
            case "pdf":
                return MediaType.APPLICATION_PDF;
            case "txt":
                return MediaType.TEXT_PLAIN;
            default:
                return MediaType.APPLICATION_OCTET_STREAM;
        }
    }
}
